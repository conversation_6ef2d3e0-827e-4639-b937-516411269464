import '@assets/public/login/login.scss';
import { createApp, ref, onMounted, defineComponent } from 'vue';
import axios from 'axios';
import type { AxiosResponse, AxiosError } from 'axios';

const Login = defineComponent({
  setup() {
    const showResetPasswordDialog = ref<boolean>(false);
    const sendPasswordRecoveryToEmail = ref<string>('');
    const emailSent = ref<boolean>(false);
    const emailSentSuccessfully = ref<boolean>(false);
    const backendMessage = ref<string>('');
    const recoveryRoute = ref<string>('');

    console.log('Login');
    onMounted(() => {
      console.log('Login mounted');
      const usernameInput = document.getElementById('username') as HTMLInputElement | null;
      if (usernameInput) {
        usernameInput.focus();
        usernameInput.select();
      }
    });

    const requestNewPassword = (event: Event): void => {
      event.preventDefault();

      const body = new URLSearchParams({
        email: sendPasswordRecoveryToEmail.value,
      });

      axios
        .post(recoveryRoute.value, body, {
          headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        })
        .then((response: AxiosResponse) => {
          emailSent.value = true;
          emailSentSuccessfully.value = response.data.successful;
          backendMessage.value = response.data.message;
        })
        .catch((error: AxiosError) => {
          console.error(error);
          emailSent.value = false;
          emailSentSuccessfully.value = false;
        });
    };

    return {
      showResetPasswordDialog,
      sendPasswordRecoveryToEmail,
      emailSent,
      emailSentSuccessfully,
      backendMessage,
      recoveryRoute,
      requestNewPassword,
    };
  },
});

const app = createApp(Login);
app.config.compilerOptions.delimiters = ['{*', '*}'];
app.mount('#login');
